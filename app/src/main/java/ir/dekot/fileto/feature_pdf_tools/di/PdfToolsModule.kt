package ir.dekot.fileto.feature_pdf_tools.di

import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import ir.dekot.fileto.feature_pdf_tools.data.repository.PdfToolsRepositoryImpl
import ir.dekot.fileto.feature_pdf_tools.domain.repository.PdfToolsRepository
import javax.inject.Singleton

/**
 * ماژول Hilt برای تزریق وابستگی‌های مربوط به ابزارهای PDF
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class PdfToolsModule {

    /**
     * تزریق Repository برای ابزارهای PDF
     */
    @Binds
    @Singleton
    abstract fun bindPdfToolsRepository(
        pdfToolsRepositoryImpl: PdfToolsRepositoryImpl
    ): PdfToolsRepository
}
